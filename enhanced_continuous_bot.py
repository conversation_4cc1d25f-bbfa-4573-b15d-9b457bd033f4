#!/usr/bin/env python3
"""
Enhanced Continuous P2P Bot with Margin Protection

Enhanced version that prevents large margins through multiple safeguards:
1. Tight margin limits (100/50 KES + 500/300 KES caps)
2. Absolute margin protection (never exceed specified limits)
3. Smart competitor selection (avoid extreme outliers)
4. Real-time margin monitoring and alerts
"""

import os
import time
import hmac
import hashlib
import requests
import logging
from urllib.parse import urlencode
from dotenv import load_dotenv
from datetime import datetime

# Load environment variables
load_dotenv()
API_KEY = os.getenv("BINANCE_API_KEY")
SECRET_KEY = os.getenv("BINANCE_API_SECRET")
MY_BUY_ADV_NO = "11554683611809857536"
MY_SELL_ADV_NO = "12625039480111222784"
BUY_MARGIN_KES = float(os.getenv("BUY_MARGIN_KES", "0.1"))  # Ultra-minimal 0.1 KES margin to beat competition
SELL_MARGIN_KES = float(os.getenv("SELL_MARGIN_KES", "0.1"))  # Ultra-minimal 0.1 KES margin to beat competition
MAX_BUY_MARGIN_KES = float(os.getenv("MAX_BUY_MARGIN_KES", "1"))  # Maximum 1 KES above competitor
MAX_SELL_MARGIN_KES = float(os.getenv("MAX_SELL_MARGIN_KES", "1"))  # Maximum 1 KES below competitor
MIN_PROFIT_THRESHOLD = float(os.getenv("MIN_PROFIT_THRESHOLD", "200"))  # Updated to 200 KES
MONITOR_INTERVAL = int(os.getenv("MONITOR_INTERVAL", "7"))

# ADDITIONAL SAFEGUARDS - Ultra-competitive margins
ABSOLUTE_MAX_BUY_MARGIN = 5  # Never exceed 5 KES above ANY competitor
ABSOLUTE_MAX_SELL_MARGIN = 5  # Never exceed 5 KES below ANY competitor

BASE_URL = "https://api.binance.com"
COMMISSION_RATE = 0.0016

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s | %(levelname)s | %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("enhanced_p2p_bot.log", encoding='utf-8')
    ]
)

# Create session
SESSION = requests.Session()
SESSION.headers.update({
    "X-MBX-APIKEY": API_KEY,
    "Content-Type": "application/json",
    "clientType": "WEB",
})

def _sign(params: dict) -> dict:
    """Return params with timestamp + signature added (HMAC‑SHA256)."""
    params["timestamp"] = int(time.time() * 1000)
    query = urlencode(params)
    signature = hmac.new(SECRET_KEY.encode(), query.encode(), hashlib.sha256).hexdigest()
    params["signature"] = signature
    return params

def get_my_ad_details(adv_no):
    """Get ad details using private API"""
    endpoint = "/sapi/v1/c2c/ads/getDetailByNo"
    params = {"adsNo": adv_no, "clientType": "WEB"}
    params = _sign(params)
    
    r = SESSION.post(BASE_URL + endpoint, params=params, timeout=10)
    r.raise_for_status()
    response_data = r.json()
    return response_data.get("data", {})

def get_competition_data():
    """Get competition data (search multiple pages)"""
    url = "https://p2p.binance.com/bapi/c2c/v2/friendly/c2c/adv/search"

    session = requests.Session()
    session.headers.update({
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    })

    # BUY competition (users sell BTC to you) - search first 2 pages
    buy_ads = []
    for page in range(1, 3):  # Pages 1-2
        buy_body = {"asset": "BTC", "fiat": "KES", "tradeType": "SELL", "page": page, "rows": 20}

        try:
            r = session.post(url, json=buy_body, timeout=10)
            r.raise_for_status()
            page_ads = r.json().get("data", [])
            if page_ads:
                buy_ads.extend(page_ads)
            else:
                break  # No more results
        except Exception as e:
            logging.warning("Failed to get BTC buy ads page %d: %s", page, e)
            break

    # SELL competition (users buy BTC from you) - search first 3 pages
    sell_ads = []
    for page in range(1, 4):  # Pages 1-3
        sell_body = {"asset": "BTC", "fiat": "KES", "tradeType": "BUY", "page": page, "rows": 20}

        try:
            r = session.post(url, json=sell_body, timeout=10)
            r.raise_for_status()
            page_ads = r.json().get("data", [])
            if page_ads:
                sell_ads.extend(page_ads)
            else:
                break  # No more results
        except Exception as e:
            logging.warning("Failed to get BTC sell ads page %d: %s", page, e)
            break

    return buy_ads, sell_ads

def filter_competition(ads, ad_type, my_adv_no):
    """Filter competition with proper minimum transaction filtering"""
    eligible = []
    my_current_price = None
    filtered_out_count = 0

    for a in ads:
        min_amount = float(a["adv"].get("minSingleTransAmount", 0))
        advertiser = a.get("advertiser", {})
        user_type = advertiser.get("userType", "")
        user_identity = advertiser.get("userIdentity", "")
        nick_name = advertiser.get("nickName", "Unknown")

        # Check if this is my ad first
        if a["adv"]["advNo"] == my_adv_no:
            my_current_price = float(a["adv"]["price"])
            continue

        # Only consider verified merchants
        if not (user_type == "merchant" and user_identity == "MASS_MERCHANT"):
            continue

        # Apply minimum transaction filters based on ad type
        if ad_type == "BUY":
            # For BUY ads: only include merchants with min transaction ≤ 5000 KES
            if min_amount > 5000:
                filtered_out_count += 1
                logging.debug("BUY: Filtered out %s (min: %.0f KES > 5000)", nick_name, min_amount)
                continue
        # For SELL ads: no minimum filter (include all verified merchants)

        # Add to eligible competitors
        eligible.append(a)

    # Log filtering results
    if filtered_out_count > 0:
        logging.info("%s: Filtered out %d merchants with min > 5000 KES", ad_type, filtered_out_count)

    # Sort by price
    if ad_type == "BUY":
        ranked = sorted(eligible, key=lambda a: float(a["adv"]["price"]), reverse=True)
    else:
        ranked = sorted(eligible, key=lambda a: float(a["adv"]["price"]))

    # Log top competitors for verification
    logging.info("BTC %s: Top eligible competitors:", ad_type)
    for i, competitor in enumerate(ranked[:5], 1):
        price = float(competitor["adv"]["price"])
        name = competitor["advertiser"].get("nickName", "Unknown")
        min_amt = float(competitor["adv"].get("minSingleTransAmount", 0))
        max_amt = float(competitor["adv"].get("maxSingleTransAmount", 0))
        logging.info("  %d. %s — %.2f KES (min: %.0f, max: %.0f)", i, name, price, min_amt, max_amt)

    return ranked, my_current_price

def calculate_safe_target_price(competitors, ad_type):
    """Calculate target price with enhanced safeguards and better competitor selection"""
    if not competitors:
        return None, "No competitors found"

    # Get price range and analyze competition
    prices = [float(c["adv"]["price"]) for c in competitors]
    competitor_names = [c["advertiser"].get("nickName", "Unknown") for c in competitors]

    # Sort prices and get key reference points
    if ad_type == "BUY":
        # For BUY: highest price is best for sellers
        sorted_competitors = sorted(zip(prices, competitor_names), reverse=True)
        best_price = sorted_competitors[0][0]
        best_name = sorted_competitors[0][1]
        worst_price = sorted_competitors[-1][0]

        # Always use the best price competitor (highest for BUY)
        reference_price = best_price
        reference_name = best_name
        logging.info("BUY: Targeting best competitor %s at %.2f KES", reference_name, reference_price)

        # Calculate ideal target
        ideal_target = reference_price + BUY_MARGIN_KES

        # Apply ultra-competitive safeguards to maintain minimal margins
        # For BUY: we want higher price, but only by minimal amount to beat competition
        safeguards = [
            ("vs reference + max margin", reference_price + MAX_BUY_MARGIN_KES),  # Don't go too far above reference
            ("absolute maximum", reference_price + ABSOLUTE_MAX_BUY_MARGIN),  # Never more than 50 KES above reference
        ]

        # Use the most restrictive safeguard (lowest price = least aggressive)
        final_target = ideal_target
        active_safeguard = "None"

        for name, limit in safeguards:
            if final_target > limit:
                final_target = limit
                active_safeguard = name
                logging.warning("BUY: Limited by %s (%.2f -> %.2f)", name, ideal_target, final_target)

        return final_target, {
            "ideal": ideal_target,
            "final": final_target,
            "active_safeguard": active_safeguard,
            "reference_competitor": reference_name,
            "reference_price": reference_price,
            "margin_above_reference": final_target - reference_price,
            "margin_above_worst": final_target - worst_price,
            "best_competitor": best_price,
            "worst_competitor": worst_price
        }

    else:  # SELL
        # For SELL: lowest price is best for buyers
        sorted_competitors = sorted(zip(prices, competitor_names))
        best_price = sorted_competitors[0][0]
        best_name = sorted_competitors[0][1]
        worst_price = sorted_competitors[-1][0]

        # Always use the best price competitor (lowest for SELL)
        reference_price = best_price
        reference_name = best_name
        logging.info("SELL: Targeting best competitor %s at %.2f KES", reference_name, reference_price)

        # Calculate ideal target
        ideal_target = reference_price - SELL_MARGIN_KES

        # Apply ultra-competitive safeguards to maintain minimal margins
        # For SELL: we want the LOWEST price, but only by minimal amount to beat competition
        safeguards = [
            ("vs reference - max margin", reference_price - MAX_SELL_MARGIN_KES),  # Don't go too far below reference
            ("absolute minimum", reference_price - ABSOLUTE_MAX_SELL_MARGIN),  # Never more than 50 KES below reference
        ]

        # Use the most restrictive safeguard (highest price = least aggressive)
        final_target = ideal_target
        active_safeguard = "None"

        for name, limit in safeguards:
            if final_target < limit:
                final_target = limit
                active_safeguard = name
                logging.warning("SELL: Limited by %s (%.2f -> %.2f)", name, ideal_target, final_target)

        return final_target, {
            "ideal": ideal_target,
            "final": final_target,
            "active_safeguard": active_safeguard,
            "reference_competitor": reference_name,
            "reference_price": reference_price,
            "margin_below_reference": reference_price - final_target,
            "margin_below_worst": worst_price - final_target,
            "best_competitor": best_price,
            "worst_competitor": worst_price
        }

def update_ad_price(adv_no, new_price, ad_type):
    """Update ad price using floating price"""
    try:
        current_ad = get_my_ad_details(adv_no)
        current_price = float(current_ad["price"])
        current_price_type = current_ad.get("priceType", 0)
        
        if abs(new_price - current_price) < 1:
            return True
        
        if current_price_type == 2:
            current_ratio = float(current_ad.get("priceFloatingRatio", "100.00000000"))
            required_ratio = current_ratio * (new_price / current_price)
            
            if ad_type == "BUY":
                buffer_ratio = required_ratio * 1.0001  # Minimal 0.01% buffer for BUY
            else:
                buffer_ratio = required_ratio * 0.9999  # Minimal 0.01% buffer for SELL
            
            payload = {"advNo": adv_no, "priceType": 2, "priceFloatingRatio": f"{buffer_ratio:.8f}"}
        else:
            target_ratio = (new_price / current_price) * 100
            if ad_type == "BUY":
                buffer_ratio = target_ratio * 1.0001  # Minimal 0.01% buffer for BUY
            else:
                buffer_ratio = target_ratio * 0.9999  # Minimal 0.01% buffer for SELL
            
            payload = {"advNo": adv_no, "priceType": 2, "priceFloatingRatio": f"{buffer_ratio:.8f}"}
        
        endpoint = "/sapi/v1/c2c/ads/update"
        timestamp = int(time.time() * 1000)
        query_params = {"timestamp": timestamp}
        query_string = urlencode(query_params)
        signature = hmac.new(SECRET_KEY.encode(), query_string.encode(), hashlib.sha256).hexdigest()
        query_params["signature"] = signature
        
        r = SESSION.post(BASE_URL + endpoint, params=query_params, json=payload, timeout=10)
        r.raise_for_status()
        resp = r.json()
        
        if resp.get("success") or resp.get("code") == "000000":
            logging.info("SUCCESS: Updated %s ad price to %.2f KES", ad_type, new_price)
            return True
        else:
            logging.error("FAILED: Update %s ad price: %s", ad_type, resp)
            return False
            
    except Exception as e:
        logging.error("ERROR: Updating %s ad price: %s", ad_type, e)
        return False

def calculate_profit(buy_price, sell_price):
    """Calculate arbitrage profit for 50,000 KES transaction"""
    investment = 50000
    buy_commission = investment * COMMISSION_RATE
    net_payment = investment - buy_commission
    btc_acquired = net_payment / buy_price
    gross_revenue = btc_acquired * sell_price
    sell_commission = gross_revenue * COMMISSION_RATE
    net_revenue = gross_revenue - sell_commission
    profit = net_revenue - net_payment
    profit_pct = (profit / net_payment) * 100
    return profit, profit_pct

def monitor_and_optimize():
    """Enhanced monitoring cycle with margin protection"""
    try:
        # Get competition data
        buy_ads, sell_ads = get_competition_data()
        buy_competitors, my_buy_price = filter_competition(buy_ads, "BUY", MY_BUY_ADV_NO)
        sell_competitors, my_sell_price = filter_competition(sell_ads, "SELL", MY_SELL_ADV_NO)

        # Debug information
        logging.info("MARKET DATA: Found %d buy competitors, %d sell competitors",
                    len(buy_competitors), len(sell_competitors))

        # Get my ad prices from API if not found in public data
        if not my_buy_price:
            try:
                buy_ad_details = get_my_ad_details(MY_BUY_ADV_NO)
                my_buy_price = float(buy_ad_details.get("price", 0))
                logging.info("MY BUY PRICE: %.2f KES (from API)", my_buy_price)
            except Exception as e:
                logging.error("Failed to get buy ad price from API: %s", e)
        else:
            logging.info("MY BUY PRICE: %.2f KES (from public data)", my_buy_price)

        if not my_sell_price:
            try:
                sell_ad_details = get_my_ad_details(MY_SELL_ADV_NO)
                my_sell_price = float(sell_ad_details.get("price", 0))
                logging.info("MY SELL PRICE: %.2f KES (from API)", my_sell_price)
            except Exception as e:
                logging.error("Failed to get sell ad price from API: %s", e)
        else:
            logging.info("MY SELL PRICE: %.2f KES (from public data)", my_sell_price)

        if not buy_competitors or not sell_competitors:
            logging.warning("WARNING: Insufficient competition data (buy: %d, sell: %d)",
                           len(buy_competitors), len(sell_competitors))
            return
        
        updates_made = False

        # BUY market analysis with enhanced safeguards
        target_buy_price, buy_analysis = calculate_safe_target_price(buy_competitors, "BUY")

        if target_buy_price and my_buy_price:
            current_margin = my_buy_price - buy_analysis["reference_price"]
            target_margin = buy_analysis["margin_above_reference"]

            # Show current status
            logging.info("BUY ANALYSIS:")
            logging.info("  Target competitor: %s at %.2f KES",
                        buy_analysis["reference_competitor"], buy_analysis["reference_price"])
            logging.info("  Current margin: %.1f KES, Target margin: %.1f KES",
                        current_margin, target_margin)

            # Update if needed (allow minimal tolerance for ultra-competitive pricing)
            if abs(my_buy_price - target_buy_price) > 0.5:  # 0.5 KES tolerance for ultra-tight competition
                logging.info("UPDATING BUY: %.2f -> %.2f KES", my_buy_price, target_buy_price)
                if buy_analysis["active_safeguard"] != "None":
                    logging.warning("  PROTECTED by %s safeguard", buy_analysis["active_safeguard"])

                update_ad_price(MY_BUY_ADV_NO, target_buy_price, "BUY")
                my_buy_price = target_buy_price
                updates_made = True
            else:
                logging.info("BUY: Price within tolerance (%.2f vs target %.2f)",
                            my_buy_price, target_buy_price)

        # SELL market analysis with enhanced safeguards
        target_sell_price, sell_analysis = calculate_safe_target_price(sell_competitors, "SELL")

        if target_sell_price and my_sell_price:
            current_margin = sell_analysis["reference_price"] - my_sell_price
            target_margin = sell_analysis["margin_below_reference"]

            # Show current status
            logging.info("SELL ANALYSIS:")
            logging.info("  Target competitor: %s at %.2f KES",
                        sell_analysis["reference_competitor"], sell_analysis["reference_price"])
            logging.info("  Current margin: %.1f KES, Target margin: %.1f KES",
                        current_margin, target_margin)

            # Update if needed (allow minimal tolerance for ultra-competitive pricing)
            if abs(my_sell_price - target_sell_price) > 0.5:  # 0.5 KES tolerance for ultra-tight competition
                logging.info("UPDATING SELL: %.2f -> %.2f KES", my_sell_price, target_sell_price)
                if sell_analysis["active_safeguard"] != "None":
                    logging.warning("  PROTECTED by %s safeguard", sell_analysis["active_safeguard"])

                update_ad_price(MY_SELL_ADV_NO, target_sell_price, "SELL")
                my_sell_price = target_sell_price
                updates_made = True
            else:
                logging.info("SELL: Price within tolerance (%.2f vs target %.2f)",
                            my_sell_price, target_sell_price)
        
        # Calculate and monitor profit (ALWAYS show profit)
        if my_buy_price and my_sell_price:
            profit, profit_pct = calculate_profit(my_buy_price, my_sell_price)

            # Always log profit information
            logging.info("PROFIT ANALYSIS:")
            logging.info("  Buy price: %.2f KES, Sell price: %.2f KES", my_buy_price, my_sell_price)
            logging.info("  Spread: %.2f KES (%.2f%%)", my_sell_price - my_buy_price,
                        ((my_sell_price - my_buy_price) / my_buy_price) * 100)
            logging.info("  Profit per 50K: %.2f KES (%.2f%%) - Threshold: %.2f KES",
                        profit, profit_pct, MIN_PROFIT_THRESHOLD)

            # Profit status
            if profit >= MIN_PROFIT_THRESHOLD:
                logging.info("  STATUS: HEALTHY (%.2f >= %.2f KES)", profit, MIN_PROFIT_THRESHOLD)

                # Show scaling potential for healthy profits
                daily_profits = [profit * x for x in [10, 20, 30]]
                monthly_profits = [x * 30 for x in daily_profits]
                logging.info("  SCALING: 10tx/day=%.0f KES/month, 20tx/day=%.0f KES/month, 30tx/day=%.0f KES/month",
                            monthly_profits[0], monthly_profits[1], monthly_profits[2])
            else:
                logging.warning("  STATUS: LOW PROFIT (%.2f < %.2f KES)", profit, MIN_PROFIT_THRESHOLD)
                logging.warning("  ALERT: Consider manually pausing buy ad or adjusting margins")
                logging.warning("  NEEDED: Increase spread by %.2f KES for healthy profit",
                               MIN_PROFIT_THRESHOLD - profit)
        else:
            logging.warning("PROFIT: Cannot calculate - missing price data")

        if updates_made:
            logging.info("MONITOR: Cycle completed with updates")
        else:
            logging.info("MONITOR: Cycle completed - no updates needed")
        
    except Exception as e:
        logging.error("ERROR: Monitoring cycle failed: %s", e)

def main():
    """Main continuous monitoring loop with enhanced margin protection"""
    logging.info("=" * 80)
    logging.info("ENHANCED CONTINUOUS P2P BOT STARTED")
    logging.info("=" * 80)
    logging.info("MARGINS: +%.1f KES (buy), -%.1f KES (sell)", BUY_MARGIN_KES, SELL_MARGIN_KES)
    logging.info("MAX MARGINS: +%.1f KES (buy cap), -%.1f KES (sell cap)", MAX_BUY_MARGIN_KES, MAX_SELL_MARGIN_KES)
    logging.info("ABSOLUTE CAPS: +%.1f KES (buy), -%.1f KES (sell)", ABSOLUTE_MAX_BUY_MARGIN, ABSOLUTE_MAX_SELL_MARGIN)
    logging.info("THRESHOLD: %.0f KES per 50K transaction", MIN_PROFIT_THRESHOLD)
    logging.info("INTERVAL: %d seconds (high frequency monitoring)", MONITOR_INTERVAL)
    logging.info("FEATURES: Ultra-competitive margins (1-10 KES), smart competitor selection")
    logging.info("CONTROL: Press Ctrl+C to stop")
    
    cycle_count = 0
    last_status_time = time.time()
    
    try:
        while True:
            cycle_count += 1
            current_time = time.time()
            
            if current_time - last_status_time >= 60:
                logging.info("")
                logging.info("=" * 60)
                logging.info("STATUS UPDATE #%d - %s", cycle_count, datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                logging.info("Ultra-competitive mode active - maintaining minimal 0.1-1 KES margins")
                logging.info("=" * 60)
                last_status_time = current_time
            
            monitor_and_optimize()
            time.sleep(MONITOR_INTERVAL)
            
    except KeyboardInterrupt:
        logging.info("")
        logging.info("=" * 80)
        logging.info("STOPPED: Enhanced bot stopped by user")
        logging.info("SUMMARY: Total cycles completed: %d", cycle_count)
        logging.info("=" * 80)
    except Exception as e:
        logging.error("FATAL ERROR: %s", e)

if __name__ == "__main__":
    main()
