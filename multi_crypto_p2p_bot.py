#!/usr/bin/env python3
"""
Multi-Cryptocurrency P2P Trading Bot

Manages 4 different cryptocurrency pairs (USDT, ETH, WLD, BNB) with enhanced margin protection:
- 4 BUY ads: USDT, ETH, WLD, BNB (filter competitors with min ≤5000 KES)
- 1 SELL ad: USDT only (filter competitors with M-PESA Safaricom only)
- Real-time conversion rates and profit analysis
- 7-second monitoring with margin protection
"""

import os
import time
import hmac
import hashlib
import requests
import logging
from urllib.parse import urlencode
from dotenv import load_dotenv
from datetime import datetime

# Load environment variables
load_dotenv()
API_KEY = os.getenv("BINANCE_API_KEY")
SECRET_KEY = os.getenv("BINANCE_API_SECRET")

# Ad Numbers for Multi-Crypto Trading
CRYPTO_ADS = {
    "USDT": {
        "buy_ad": "11554687695418343424",
        "sell_ad": "12625091404848156672"
    },
    "BNB": {
        "buy_ad": "11549670491191529472"
    },
    "WLD": {
        "buy_ad": "12667792337659699200"
    },
    "ETH": {
        "buy_ad": "11554673685470920704"
    }
}

# Trading Configuration - MINIMAL MARGINS for tight competition
BUY_MARGIN_KES = float(os.getenv("BUY_MARGIN_KES", "0.01"))  # Minimal margin: 0.01 KES
SELL_MARGIN_KES = float(os.getenv("SELL_MARGIN_KES", "0.01"))  # Minimal margin: 0.01 KES
MIN_PROFIT_THRESHOLD = float(os.getenv("MIN_PROFIT_THRESHOLD", "200"))
MONITOR_INTERVAL = int(os.getenv("MONITOR_INTERVAL", "7"))

# Sell ad filtering criteria
MIN_TRANSACTION_AMOUNT = 5000  # Minimum transaction amount for sell competitors
MIN_MAX_LIMIT = 20000  # Minimum maximum limit for sell competitors (substantial volume)

BASE_URL = "https://api.binance.com"
COMMISSION_RATE = 0.0016

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s | %(levelname)s | %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("multi_crypto_p2p_bot.log", encoding='utf-8')
    ]
)

# Create session
SESSION = requests.Session()
SESSION.headers.update({
    "X-MBX-APIKEY": API_KEY,
    "Content-Type": "application/json",
    "clientType": "WEB",
})

def _sign(params: dict) -> dict:
    """Return params with timestamp + signature added (HMAC‑SHA256)."""
    params["timestamp"] = int(time.time() * 1000)
    query = urlencode(params)
    signature = hmac.new(SECRET_KEY.encode(), query.encode(), hashlib.sha256).hexdigest()
    params["signature"] = signature
    return params

def get_conversion_rates():
    """Get real-time conversion rates for all crypto pairs to USDT"""
    try:
        # Get conversion rates from Binance API
        url = f"{BASE_URL}/api/v3/ticker/price"
        
        pairs = ["ETHUSDT", "WLDUSDT", "BNBUSDT"]
        rates = {"USDT": 1.0}  # USDT to USDT is 1:1
        
        for pair in pairs:
            response = requests.get(f"{url}?symbol={pair}", timeout=10)
            if response.status_code == 200:
                data = response.json()
                crypto = pair.replace("USDT", "")
                rates[crypto] = float(data["price"])
            else:
                logging.error("Failed to get %s rate", pair)
                rates[pair.replace("USDT", "")] = 0.0
        
        return rates
    except Exception as e:
        logging.error("Error getting conversion rates: %s", e)
        return {"USDT": 1.0, "ETH": 0.0, "WLD": 0.0, "BNB": 0.0}

def get_my_ad_details(adv_no):
    """Get ad details using private API"""
    endpoint = "/sapi/v1/c2c/ads/getDetailByNo"
    params = {"adsNo": adv_no, "clientType": "WEB"}
    params = _sign(params)
    
    r = SESSION.post(BASE_URL + endpoint, params=params, timeout=10)
    r.raise_for_status()
    response_data = r.json()
    return response_data.get("data", {})

def get_competition_data(crypto):
    """Get competition data for specific cryptocurrency (search multiple pages)"""
    url = "https://p2p.binance.com/bapi/c2c/v2/friendly/c2c/adv/search"

    session = requests.Session()
    session.headers.update({
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    })

    # BUY competition (users sell crypto to you) - search first 2 pages
    buy_ads = []
    for page in range(1, 3):  # Pages 1-2
        buy_body = {
            "asset": crypto,
            "fiat": "KES",
            "tradeType": "SELL",
            "page": page,
            "rows": 20,
        }

        try:
            r = session.post(url, json=buy_body, timeout=10)
            r.raise_for_status()
            page_ads = r.json().get("data", [])
            if page_ads:
                buy_ads.extend(page_ads)
            else:
                break  # No more results
        except Exception as e:
            logging.warning("Failed to get buy ads page %d for %s: %s", page, crypto, e)
            break

    # SELL competition (only for USDT) - search first 3 pages to find Miss_Del
    sell_ads = []
    if crypto == "USDT":
        for page in range(1, 4):  # Pages 1-3
            sell_body = {
                "asset": "USDT",
                "fiat": "KES",
                "tradeType": "BUY",
                "page": page,
                "rows": 20,
            }

            try:
                r = session.post(url, json=sell_body, timeout=10)
                r.raise_for_status()
                page_ads = r.json().get("data", [])
                if page_ads:
                    sell_ads.extend(page_ads)
                else:
                    break  # No more results
            except Exception as e:
                logging.warning("Failed to get sell ads page %d for USDT: %s", page, e)
                break

    return buy_ads, sell_ads

def filter_competition(ads, ad_type, my_adv_no, crypto):
    """Filter competition with proper filtering logic"""
    eligible = []
    my_current_price = None
    filtered_out_count = 0
    
    for a in ads:
        min_amount = float(a["adv"].get("minSingleTransAmount", 0))
        advertiser = a.get("advertiser", {})
        user_type = advertiser.get("userType", "")
        user_identity = advertiser.get("userIdentity", "")
        nick_name = advertiser.get("nickName", "Unknown")
        
        # Check if this is my ad first
        if a["adv"]["advNo"] == my_adv_no:
            my_current_price = float(a["adv"]["price"])
            continue
        
        # Only consider verified merchants
        if not (user_type == "merchant" and user_identity == "MASS_MERCHANT"):
            continue
        
        # Apply filters based on ad type
        if ad_type == "BUY":
            # For BUY ads: only include merchants with min transaction ≤ 5000 KES
            if min_amount > 5000:
                filtered_out_count += 1
                continue
        elif ad_type == "SELL" and crypto == "USDT":
            # For SELL ads (USDT only): Filter by M-PESA Safaricom + substantial volume
            max_amount = float(a["adv"].get("maxSingleTransAmount", 0))

            # Filter 1: Must have max limit ≥ 20000 KES (substantial volume)
            if max_amount < MIN_MAX_LIMIT:
                filtered_out_count += 1
                continue

            # Filter 2: Must have M-PESA Kenya (Safaricom) payment method
            payment_methods = a["adv"].get("tradeMethods", [])
            has_mpesa_safaricom = any("M-PESA Kenya (Safaricom)" in method.get("tradeMethodName", "")
                                    for method in payment_methods)
            if not has_mpesa_safaricom:
                filtered_out_count += 1
                continue
        
        eligible.append(a)
    
    # Log filtering results
    if filtered_out_count > 0:
        if ad_type == "BUY":
            filter_type = "min > 5000 KES"
        else:
            filter_type = "max < 20000 KES or non-M-PESA Kenya (Safaricom)"
        logging.info("%s %s: Filtered out %d merchants (%s)", crypto, ad_type, filtered_out_count, filter_type)
    
    # Sort by price
    if ad_type == "BUY":
        ranked = sorted(eligible, key=lambda a: float(a["adv"]["price"]), reverse=True)
    else:
        ranked = sorted(eligible, key=lambda a: float(a["adv"]["price"]))

    # Log top competitors for verification
    logging.info("%s %s: Top eligible competitors:", crypto, ad_type)
    for i, competitor in enumerate(ranked[:5], 1):
        price = float(competitor["adv"]["price"])
        name = competitor["advertiser"].get("nickName", "Unknown")
        min_amt = float(competitor["adv"].get("minSingleTransAmount", 0))
        max_amt = float(competitor["adv"].get("maxSingleTransAmount", 0))
        logging.info("  %d. %s — %.2f KES (min: %.0f, max: %.0f)", i, name, price, min_amt, max_amt)

    return ranked, my_current_price

def calculate_safe_target_price(competitors, ad_type, crypto):
    """Calculate target price with enhanced safeguards"""
    if not competitors:
        return None, f"No competitors found for {crypto} {ad_type}"
    
    # Get price range and analyze competition
    prices = [float(c["adv"]["price"]) for c in competitors]
    competitor_names = [c["advertiser"].get("nickName", "Unknown") for c in competitors]
    
    # Sort prices and get key reference points
    if ad_type == "BUY":
        sorted_competitors = sorted(zip(prices, competitor_names), reverse=True)
        best_price = sorted_competitors[0][0]
        best_name = sorted_competitors[0][1]
        
        # Always use the best price competitor (highest for BUY)
        reference_price = best_price
        reference_name = best_name
        
        # Calculate target with minimal margin (e.g., 129.52 -> 129.53)
        final_target = reference_price + BUY_MARGIN_KES

        # No complex safeguards needed with minimal margins
        active_safeguard = "None"
        
        return final_target, {
            "ideal": final_target,
            "final": final_target,
            "active_safeguard": active_safeguard,
            "reference_competitor": reference_name,
            "reference_price": reference_price,
            "margin_above_reference": final_target - reference_price,
        }
    
    else:  # SELL (USDT only)
        sorted_competitors = sorted(zip(prices, competitor_names))
        best_price = sorted_competitors[0][0]
        best_name = sorted_competitors[0][1]
        
        # Always use the best price competitor (lowest for SELL)
        reference_price = best_price
        reference_name = best_name
        
        # Calculate target with minimal margin (e.g., 129.52 -> 129.51)
        final_target = reference_price - SELL_MARGIN_KES

        # No complex safeguards needed with minimal margins
        active_safeguard = "None"
        
        return final_target, {
            "ideal": final_target,
            "final": final_target,
            "active_safeguard": active_safeguard,
            "reference_competitor": reference_name,
            "reference_price": reference_price,
            "margin_below_reference": reference_price - final_target,
        }

def update_ad_price(adv_no, new_price, ad_type, crypto):
    """Update ad price using floating price"""
    try:
        current_ad = get_my_ad_details(adv_no)
        current_price = float(current_ad["price"])
        current_price_type = current_ad.get("priceType", 0)
        
        if abs(new_price - current_price) < 1:
            return True
        
        if current_price_type == 2:
            current_ratio = float(current_ad.get("priceFloatingRatio", "100.00000000"))
            required_ratio = current_ratio * (new_price / current_price)
            
            if ad_type == "BUY":
                buffer_ratio = required_ratio * 1.001
            else:
                buffer_ratio = required_ratio * 0.999
            
            payload = {"advNo": adv_no, "priceType": 2, "priceFloatingRatio": f"{buffer_ratio:.8f}"}
        else:
            target_ratio = (new_price / current_price) * 100
            if ad_type == "BUY":
                buffer_ratio = target_ratio * 1.001
            else:
                buffer_ratio = target_ratio * 0.999
            
            payload = {"advNo": adv_no, "priceType": 2, "priceFloatingRatio": f"{buffer_ratio:.8f}"}
        
        endpoint = "/sapi/v1/c2c/ads/update"
        timestamp = int(time.time() * 1000)
        query_params = {"timestamp": timestamp}
        query_string = urlencode(query_params)
        signature = hmac.new(SECRET_KEY.encode(), query_string.encode(), hashlib.sha256).hexdigest()
        query_params["signature"] = signature
        
        r = SESSION.post(BASE_URL + endpoint, params=query_params, json=payload, timeout=10)
        r.raise_for_status()
        resp = r.json()
        
        if resp.get("success") or resp.get("code") == "000000":
            logging.info("SUCCESS: Updated %s %s ad price to %.2f KES", crypto, ad_type, new_price)
            return True
        else:
            logging.error("FAILED: Update %s %s ad price: %s", crypto, ad_type, resp)
            return False
            
    except Exception as e:
        logging.error("ERROR: Updating %s %s ad price: %s", crypto, ad_type, e)
        return False

def calculate_crypto_profit(buy_price_kes, crypto, conversion_rates, usdt_sell_price_kes):
    """Calculate profit for crypto -> USDT -> KES arbitrage"""
    investment = 50000  # 50,000 KES

    # Step 1: Buy crypto with KES
    buy_commission = investment * COMMISSION_RATE
    net_payment = investment - buy_commission
    crypto_acquired = net_payment / buy_price_kes

    # Step 2: Convert crypto to USDT
    usdt_rate = conversion_rates.get(crypto, 0)
    if usdt_rate == 0:
        return 0, 0, 0  # Cannot convert

    usdt_acquired = crypto_acquired * usdt_rate

    # Step 3: Sell USDT for KES
    gross_revenue = usdt_acquired * usdt_sell_price_kes
    sell_commission = gross_revenue * COMMISSION_RATE
    net_revenue = gross_revenue - sell_commission

    # Calculate profit
    profit = net_revenue - net_payment
    profit_pct = (profit / net_payment) * 100

    return profit, profit_pct, usdt_acquired

def monitor_crypto_pair(crypto, conversion_rates, usdt_sell_price):
    """Monitor and optimize a single crypto pair"""
    try:
        # Get competition data
        buy_ads, _ = get_competition_data(crypto)

        # Get buy ad number
        buy_ad_no = CRYPTO_ADS[crypto]["buy_ad"]

        # Filter buy competition
        buy_competitors, my_buy_price = filter_competition(buy_ads, "BUY", buy_ad_no, crypto)

        if not buy_competitors:
            logging.warning("%s BUY: No eligible competitors found", crypto)
            return None

        # Get my current price from API if not found in public data
        if not my_buy_price:
            try:
                buy_ad_details = get_my_ad_details(buy_ad_no)
                my_buy_price = float(buy_ad_details.get("price", 0))
            except Exception as e:
                logging.error("%s BUY: Failed to get price from API: %s", crypto, e)
                return None

        # Calculate target price
        target_buy_price, buy_analysis = calculate_safe_target_price(buy_competitors, "BUY", crypto)

        if not target_buy_price:
            logging.warning("%s BUY: Could not calculate target price", crypto)
            return None

        # Show analysis
        current_margin = my_buy_price - buy_analysis["reference_price"]
        target_margin = buy_analysis["margin_above_reference"]

        logging.info("%s BUY ANALYSIS:", crypto)
        logging.info("  Target competitor: %s at %.2f KES",
                    buy_analysis["reference_competitor"], buy_analysis["reference_price"])
        logging.info("  Current margin: %.0f KES, Target margin: %.0f KES",
                    current_margin, target_margin)

        # Update if needed
        updates_made = False
        if abs(my_buy_price - target_buy_price) > 10:
            logging.info("UPDATING %s BUY: %.2f -> %.2f KES", crypto, my_buy_price, target_buy_price)
            if buy_analysis["active_safeguard"] != "None":
                logging.warning("  PROTECTED by %s safeguard", buy_analysis["active_safeguard"])

            success = update_ad_price(buy_ad_no, target_buy_price, "BUY", crypto)
            if success:
                my_buy_price = target_buy_price
                updates_made = True
        else:
            logging.info("%s BUY: Price within tolerance", crypto)

        # Calculate profit for this crypto path
        if usdt_sell_price and my_buy_price:
            profit, profit_pct, usdt_amount = calculate_crypto_profit(
                my_buy_price, crypto, conversion_rates, usdt_sell_price)

            return {
                "crypto": crypto,
                "buy_price": my_buy_price,
                "target_buy_price": target_buy_price,
                "profit": profit,
                "profit_pct": profit_pct,
                "usdt_amount": usdt_amount,
                "conversion_rate": conversion_rates.get(crypto, 0),
                "updates_made": updates_made,
                "competitor": buy_analysis["reference_competitor"],
                "competitor_price": buy_analysis["reference_price"]
            }

        return None

    except Exception as e:
        logging.error("Error monitoring %s: %s", crypto, e)
        return None

def monitor_usdt_sell():
    """Monitor and optimize USDT sell ad"""
    try:
        crypto = "USDT"
        sell_ad_no = CRYPTO_ADS[crypto]["sell_ad"]

        # Get competition data
        _, sell_ads = get_competition_data(crypto)

        # Filter sell competition
        sell_competitors, my_sell_price = filter_competition(sell_ads, "SELL", sell_ad_no, crypto)

        if not sell_competitors:
            logging.warning("USDT SELL: No eligible competitors found")
            return None

        # Get my current price from API if not found in public data
        if not my_sell_price:
            try:
                sell_ad_details = get_my_ad_details(sell_ad_no)
                my_sell_price = float(sell_ad_details.get("price", 0))
            except Exception as e:
                logging.error("USDT SELL: Failed to get price from API: %s", e)
                return None

        # Calculate target price
        target_sell_price, sell_analysis = calculate_safe_target_price(sell_competitors, "SELL", crypto)

        if not target_sell_price:
            logging.warning("USDT SELL: Could not calculate target price")
            return None

        # Show analysis
        current_margin = sell_analysis["reference_price"] - my_sell_price
        target_margin = sell_analysis["margin_below_reference"]

        logging.info("USDT SELL ANALYSIS:")
        logging.info("  Target competitor: %s at %.2f KES",
                    sell_analysis["reference_competitor"], sell_analysis["reference_price"])
        logging.info("  Current margin: %.0f KES, Target margin: %.0f KES",
                    current_margin, target_margin)

        # Update if needed
        updates_made = False
        if abs(my_sell_price - target_sell_price) > 10:
            logging.info("UPDATING USDT SELL: %.2f -> %.2f KES", my_sell_price, target_sell_price)
            if sell_analysis["active_safeguard"] != "None":
                logging.warning("  PROTECTED by %s safeguard", sell_analysis["active_safeguard"])

            success = update_ad_price(sell_ad_no, target_sell_price, "SELL", crypto)
            if success:
                my_sell_price = target_sell_price
                updates_made = True
        else:
            logging.info("USDT SELL: Price within tolerance")

        return {
            "sell_price": my_sell_price,
            "target_sell_price": target_sell_price,
            "updates_made": updates_made,
            "competitor": sell_analysis["reference_competitor"],
            "competitor_price": sell_analysis["reference_price"]
        }

    except Exception as e:
        logging.error("Error monitoring USDT SELL: %s", e)
        return None

def monitor_and_optimize():
    """Enhanced monitoring cycle for multi-crypto trading"""
    try:
        logging.info("=" * 80)
        logging.info("MULTI-CRYPTO MONITORING CYCLE")
        logging.info("=" * 80)

        # Get real-time conversion rates
        conversion_rates = get_conversion_rates()
        logging.info("CONVERSION RATES:")
        for crypto, rate in conversion_rates.items():
            if crypto == "USDT":
                logging.info("  %s/USDT: %.4f (1:1)", crypto, rate)
            else:
                logging.info("  %s/USDT: %.4f", crypto, rate)

        # Monitor USDT sell ad first
        usdt_sell_data = monitor_usdt_sell()
        usdt_sell_price = usdt_sell_data["sell_price"] if usdt_sell_data else None

        if not usdt_sell_price:
            logging.error("Cannot proceed without USDT sell price")
            return

        # Monitor all crypto buy ads
        crypto_results = []
        total_updates = 0

        for crypto in ["USDT", "ETH", "WLD", "BNB"]:
            logging.info("\n" + "-" * 60)
            result = monitor_crypto_pair(crypto, conversion_rates, usdt_sell_price)
            if result:
                crypto_results.append(result)
                if result["updates_made"]:
                    total_updates += 1

        # Add USDT sell update count
        if usdt_sell_data and usdt_sell_data["updates_made"]:
            total_updates += 1

        # Comprehensive profit analysis
        logging.info("\n" + "=" * 80)
        logging.info("MULTI-CRYPTO PROFIT ANALYSIS")
        logging.info("=" * 80)

        profitable_pairs = []
        total_profit_potential = 0

        for result in crypto_results:
            crypto = result["crypto"]
            profit = result["profit"]
            profit_pct = result["profit_pct"]
            conversion_rate = result["conversion_rate"]

            status = "HEALTHY" if profit >= MIN_PROFIT_THRESHOLD else "LOW"
            logging.info("%s ARBITRAGE:", crypto)
            logging.info("  Buy price: %.2f KES", result["buy_price"])
            logging.info("  Conversion rate: %.4f %s/USDT", conversion_rate, crypto)
            logging.info("  USDT sell price: %.2f KES", usdt_sell_price)
            logging.info("  Profit per 50K: %.2f KES (%.2f%%) - %s", profit, profit_pct, status)

            if profit >= MIN_PROFIT_THRESHOLD:
                profitable_pairs.append(crypto)
                total_profit_potential += profit

        # Summary and scaling analysis
        logging.info("\n" + "-" * 60)
        logging.info("SUMMARY:")
        logging.info("  Profitable pairs: %s", ", ".join(profitable_pairs) if profitable_pairs else "None")
        logging.info("  Total profit potential: %.2f KES per 50K (across all pairs)", total_profit_potential)
        logging.info("  Updates made: %d ads updated", total_updates)

        if profitable_pairs:
            # Scaling projections for multi-crypto strategy
            daily_scenarios = [5, 10, 20]  # transactions per day per crypto
            logging.info("\nMULTI-CRYPTO SCALING POTENTIAL:")
            for daily_txns in daily_scenarios:
                daily_profit = total_profit_potential * daily_txns * len(profitable_pairs)
                monthly_profit = daily_profit * 30
                logging.info("  %d tx/day/crypto (%d total): %.0f KES/day, %.0f KES/month",
                           daily_txns, daily_txns * len(profitable_pairs), daily_profit, monthly_profit)

        # Alerts for unprofitable pairs
        unprofitable = [r["crypto"] for r in crypto_results if r["profit"] < MIN_PROFIT_THRESHOLD]
        if unprofitable:
            logging.warning("\nALERT: Unprofitable pairs: %s", ", ".join(unprofitable))
            logging.warning("Consider pausing buy ads for these cryptocurrencies")

        logging.info("\nMONITOR: Multi-crypto cycle completed")

    except Exception as e:
        logging.error("ERROR: Multi-crypto monitoring failed: %s", e)

def main():
    """Main continuous monitoring loop for multi-crypto trading"""
    logging.info("=" * 80)
    logging.info("MULTI-CRYPTOCURRENCY P2P TRADING BOT STARTED")
    logging.info("=" * 80)
    logging.info("CRYPTOCURRENCIES: USDT, ETH, WLD, BNB")
    logging.info("STRATEGY: Buy all cryptos, sell USDT only (with conversions)")
    logging.info("MARGINS: +%.0f KES (buy), -%.0f KES (sell)", BUY_MARGIN_KES, SELL_MARGIN_KES)
    logging.info("FILTERING: Buy ads ≤5000 KES min, Sell ads M-PESA Safaricom only")
    logging.info("THRESHOLD: %.0f KES per 50K transaction", MIN_PROFIT_THRESHOLD)
    logging.info("INTERVAL: %d seconds", MONITOR_INTERVAL)
    logging.info("CONTROL: Press Ctrl+C to stop")

    cycle_count = 0
    last_status_time = time.time()

    try:
        while True:
            cycle_count += 1
            current_time = time.time()

            if current_time - last_status_time >= 60:
                logging.info("\n" + "=" * 60)
                logging.info("STATUS UPDATE #%d - %s", cycle_count, datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                logging.info("Multi-crypto arbitrage monitoring active")
                logging.info("=" * 60)
                last_status_time = current_time

            monitor_and_optimize()
            time.sleep(MONITOR_INTERVAL)

    except KeyboardInterrupt:
        logging.info("\n" + "=" * 80)
        logging.info("STOPPED: Multi-crypto bot stopped by user")
        logging.info("SUMMARY: Total cycles completed: %d", cycle_count)
        logging.info("=" * 80)
    except Exception as e:
        logging.error("FATAL ERROR: %s", e)

if __name__ == "__main__":
    main()
